"""
Series browser view for IPTV Player
"""

from typing import List, Optional
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import <PERSON><PERSON><PERSON><PERSON><PERSON>iew
from kivymd.uix.gridlayout import MD<PERSON>ridLayout
from kivymd.uix.card import <PERSON><PERSON><PERSON>
from kivymd.uix.label import MD<PERSON>abel
from kivymd.uix.button import MDIconButton, MDFlatButton
from kivymd.uix.chip import <PERSON><PERSON><PERSON>
from kivymd.uix.dialog import MDDialog
from kivymd.uix.progressbar import MD<PERSON>rogressBar
from kivymd.uix.list import MDList, TwoLineAvatarIconListItem
from kivymd.uix.expansionpanel import MDExpansionPanel, MDExpansionPanelOneLine
from kivy.metrics import dp

from ..core.logger import LoggerMixin
from ..models.content import Series, Season, Episode, ContentType
from ..models.user_preferences import ViewMode, SortOrder


class SeriesBrowserView(MDBoxLayout, LoggerMixin):
    """View for browsing series content"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.current_playlist = None
        self.series_list = []
        self.filtered_series = []
        self.current_category = None
        self.view_mode = ViewMode.GRID
        self.sort_order = SortOrder.NAME_ASC
        self.show_favorites_only = False

        self.build_ui()

    def build_ui(self):
        """Build the series browser UI"""
        # Filter and sort toolbar
        filter_toolbar = self.create_filter_toolbar()
        self.add_widget(filter_toolbar)

        # Category chips
        self.category_scroll = MDScrollView(
            size_hint_y=None,
            height=dp(60)
        )
        self.category_layout = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(8),
            padding=[dp(16), dp(8)],
            size_hint_x=None,
            adaptive_width=True
        )
        self.category_scroll.add_widget(self.category_layout)
        self.add_widget(self.category_scroll)

        # Content area
        self.content_scroll = MDScrollView()
        self.content_container = MDBoxLayout(orientation='vertical')
        self.content_scroll.add_widget(self.content_container)
        self.add_widget(self.content_scroll)

        # Initially show empty state
        self.show_empty_state()

    def create_filter_toolbar(self):
        """Create filter and sort toolbar"""
        toolbar = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            padding=[dp(16), dp(8)],
            spacing=dp(8)
        )

        # View mode toggle
        self.view_mode_button = MDIconButton(
            icon="view-grid",
            on_release=self.toggle_view_mode
        )
        toolbar.add_widget(self.view_mode_button)

        # Sort button
        self.sort_button = MDFlatButton(
            text="Name ↑",
            on_release=self.show_sort_menu
        )
        toolbar.add_widget(self.sort_button)

        # Spacer
        toolbar.add_widget(MDLabel())

        # Favorites filter
        self.favorites_button = MDIconButton(
            icon="heart-outline",
            on_release=self.toggle_favorites_filter
        )
        toolbar.add_widget(self.favorites_button)

        # Search button
        search_button = MDIconButton(
            icon="magnify",
            on_release=self.show_search
        )
        toolbar.add_widget(search_button)

        return toolbar

    def load_playlist(self, playlist):
        """Load series content from playlist"""
        self.current_playlist = playlist
        self.logger.info(f"Loading series for playlist: {playlist.name}")

        # TODO: Load series from database
        self.series_list = []
        self.update_categories()
        self.filter_and_sort_series()

    def update_categories(self):
        """Update category chips"""
        self.category_layout.clear_widgets()

        if not self.series_list:
            return

        # Get unique categories
        categories = set()
        for series in self.series_list:
            category = series.genre or "Uncategorized"
            categories.add(category)

        # Add "All" chip
        all_chip = MDChip(
            text="All",
            selected=self.current_category is None,
            on_release=lambda x: self.select_category(None)
        )
        self.category_layout.add_widget(all_chip)

        # Add category chips
        for category in sorted(categories):
            chip = MDChip(
                text=category,
                selected=self.current_category == category,
                on_release=lambda x, cat=category: self.select_category(cat)
            )
            self.category_layout.add_widget(chip)

    def select_category(self, category):
        """Select a category filter"""
        self.current_category = category
        self.update_category_selection()
        self.filter_and_sort_series()

    def update_category_selection(self):
        """Update category chip selection"""
        for chip in self.category_layout.children:
            if isinstance(chip, MDChip):
                if chip.text == "All":
                    chip.selected = self.current_category is None
                else:
                    chip.selected = chip.text == self.current_category

    def filter_and_sort_series(self):
        """Filter and sort series based on current settings"""
        # Start with all series
        filtered = self.series_list.copy()

        # Apply category filter
        if self.current_category:
            filtered = [s for s in filtered if (s.genre or "Uncategorized") == self.current_category]

        # Apply favorites filter
        if self.show_favorites_only:
            filtered = [s for s in filtered if s.is_favorite]

        # Sort series
        if self.sort_order == SortOrder.NAME_ASC:
            filtered.sort(key=lambda s: s.title.lower())
        elif self.sort_order == SortOrder.NAME_DESC:
            filtered.sort(key=lambda s: s.title.lower(), reverse=True)
        elif self.sort_order == SortOrder.FAVORITES_FIRST:
            filtered.sort(key=lambda s: (not s.is_favorite, s.title.lower()))

        self.filtered_series = filtered
        self.update_series_display()

    def update_series_display(self):
        """Update series display based on view mode"""
        self.content_container.clear_widgets()

        if not self.filtered_series:
            self.show_empty_state()
            return

        if self.view_mode == ViewMode.GRID:
            self.show_grid_view()
        else:
            self.show_list_view()

    def show_grid_view(self):
        """Show series in grid view"""
        grid = MDGridLayout(
            cols=3,
            spacing=dp(12),
            padding=dp(16),
            size_hint_y=None,
            adaptive_height=True
        )

        for series in self.filtered_series:
            card = self.create_series_card(series)
            grid.add_widget(card)

        self.content_container.add_widget(grid)

    def show_list_view(self):
        """Show series in list view"""
        series_list = MDList()

        for series in self.filtered_series:
            item = TwoLineAvatarIconListItem(
                text=series.title,
                secondary_text=f"{series.total_seasons} seasons • {series.total_episodes} episodes",
                on_release=lambda x, s=series: self.show_series_details(s)
            )

            # Series poster or icon
            if series.poster:
                # TODO: Load series poster
                pass

            # Favorite button
            fav_button = MDIconButton(
                icon="heart" if series.is_favorite else "heart-outline",
                theme_icon_color="Custom",
                icon_color=self.theme_cls.accent_color if series.is_favorite else self.theme_cls.primary_color,
                on_release=lambda x, s=series: self.toggle_favorite(s)
            )
            item.add_widget(fav_button)

            series_list.add_widget(item)

        self.content_container.add_widget(series_list)

    def create_series_card(self, series: Series):
        """Create series card for grid view"""
        card = MDCard(
            orientation='vertical',
            size_hint_y=None,
            height=dp(300),
            elevation=3,
            on_release=lambda x: self.show_series_details(series)
        )

        # Poster area
        poster_area = MDCard(
            size_hint_y=None,
            height=dp(200),
            md_bg_color=self.theme_cls.surface_color
        )

        if series.poster:
            # TODO: Load poster image
            poster_placeholder = MDLabel(
                text="📺",
                halign="center",
                font_size="48sp"
            )
        else:
            poster_placeholder = MDLabel(
                text=series.title[:3].upper(),
                halign="center",
                theme_text_color="Primary",
                font_style="H4"
            )

        poster_area.add_widget(poster_placeholder)
        card.add_widget(poster_area)

        # Series info
        info_layout = MDBoxLayout(
            orientation='vertical',
            padding=dp(8),
            spacing=dp(4)
        )

        # Title
        title_label = MDLabel(
            text=series.title,
            theme_text_color="Primary",
            font_style="Subtitle2",
            size_hint_y=None,
            height=dp(20)
        )
        info_layout.add_widget(title_label)

        # Season and episode count
        count_label = MDLabel(
            text=f"{series.total_seasons} seasons • {series.total_episodes} episodes",
            theme_text_color="Secondary",
            font_style="Caption",
            size_hint_y=None,
            height=dp(16)
        )
        info_layout.add_widget(count_label)

        # Year
        if series.year:
            year_label = MDLabel(
                text=str(series.year),
                theme_text_color="Secondary",
                font_style="Caption",
                size_hint_y=None,
                height=dp(16)
            )
            info_layout.add_widget(year_label)

        # Action buttons
        button_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(32),
            spacing=dp(8)
        )

        # Favorite button
        fav_button = MDIconButton(
            icon="heart" if series.is_favorite else "heart-outline",
            theme_icon_color="Custom",
            icon_color=self.theme_cls.accent_color if series.is_favorite else self.theme_cls.primary_color,
            on_release=lambda x: self.toggle_favorite(series)
        )
        button_layout.add_widget(fav_button)

        # Spacer
        button_layout.add_widget(MDLabel())

        # Play button (play first episode)
        play_button = MDIconButton(
            icon="play",
            theme_icon_color="Custom",
            icon_color=self.theme_cls.primary_color,
            on_release=lambda x: self.play_first_episode(series)
        )
        button_layout.add_widget(play_button)

        info_layout.add_widget(button_layout)
        card.add_widget(info_layout)

        return card

    def show_series_details(self, series: Series):
        """Show detailed series information with seasons and episodes"""
        content_layout = MDBoxLayout(
            orientation='vertical',
            spacing=dp(12),
            size_hint_y=None,
            adaptive_height=True
        )

        # Series title and metadata
        title_label = MDLabel(
            text=series.title,
            theme_text_color="Primary",
            font_style="H6"
        )
        content_layout.add_widget(title_label)

        # Year and episode count
        meta_parts = []
        if series.year:
            meta_parts.append(str(series.year))
        meta_parts.append(f"{series.total_seasons} seasons")
        meta_parts.append(f"{series.total_episodes} episodes")

        meta_label = MDLabel(
            text=" • ".join(meta_parts),
            theme_text_color="Secondary",
            font_style="Body2"
        )
        content_layout.add_widget(meta_label)

        # Description
        if series.description:
            desc_label = MDLabel(
                text=series.description,
                theme_text_color="Primary",
                font_style="Body1"
            )
            content_layout.add_widget(desc_label)

        # Genre and country
        if series.genre or series.country:
            info_parts = []
            if series.genre:
                info_parts.append(f"Genre: {series.genre}")
            if series.country:
                info_parts.append(f"Country: {series.country}")

            info_label = MDLabel(
                text=" • ".join(info_parts),
                theme_text_color="Secondary",
                font_style="Caption"
            )
            content_layout.add_widget(info_label)

        # Seasons and episodes
        if series.seasons:
            seasons_label = MDLabel(
                text="Seasons & Episodes:",
                theme_text_color="Primary",
                font_style="Subtitle2"
            )
            content_layout.add_widget(seasons_label)

            # Create expansion panels for seasons
            for season in sorted(series.seasons, key=lambda s: s.season_number):
                season_panel = self.create_season_panel(season)
                content_layout.add_widget(season_panel)

        # Dialog buttons
        buttons = [
            MDFlatButton(
                text="CLOSE",
                on_release=lambda x: dialog.dismiss()
            ),
            MDFlatButton(
                text="PLAY FIRST",
                on_release=lambda x: self.play_first_episode(series, dialog)
            )
        ]

        # Create scroll view and add content
        scroll_view = MDScrollView(
            size_hint_y=None,
            height=dp(400)
        )
        scroll_view.add_widget(content_layout)

        dialog = MDDialog(
            title="Series Details",
            type="custom",
            content_cls=scroll_view,
            buttons=buttons
        )
        dialog.open()

    def create_season_panel(self, season: Season):
        """Create expansion panel for season"""
        # Season content
        season_content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(4),
            size_hint_y=None,
            adaptive_height=True
        )

        # Episodes list
        for episode in sorted(season.episodes, key=lambda e: e.episode_number):
            episode_item = self.create_episode_item(episode)
            season_content.add_widget(episode_item)

        # Create expansion panel
        panel = MDExpansionPanel(
            icon="television-classic",
            content=season_content,
            panel_cls=MDExpansionPanelOneLine(
                text=f"Season {season.season_number} ({len(season.episodes)} episodes)"
            )
        )

        return panel

    def create_episode_item(self, episode: Episode):
        """Create episode list item"""
        item = TwoLineAvatarIconListItem(
            text=f"{episode.episode_number}. {episode.title}",
            secondary_text=episode.get_formatted_duration() if episode.duration else "Unknown duration",
            on_release=lambda x: self.play_episode(episode)
        )

        # Episode thumbnail placeholder
        thumbnail = MDLabel(
            text=str(episode.episode_number),
            size_hint_x=None,
            width=dp(40),
            halign="center",
            theme_text_color="Primary",
            font_style="H6"
        )
        item.add_widget(thumbnail)

        # Play button
        play_button = MDIconButton(
            icon="play",
            theme_icon_color="Custom",
            icon_color=self.theme_cls.primary_color,
            on_release=lambda x: self.play_episode(episode)
        )
        item.add_widget(play_button)

        # Progress bar for watched episodes
        if episode.watch_progress > 0:
            progress_bar = MDProgressBar(
                value=episode.watch_progress,
                size_hint_y=None,
                height=dp(4),
                pos_hint={'center_x': 0.5, 'y': 0}
            )
            item.add_widget(progress_bar)

        return item

    def play_episode(self, episode: Episode):
        """Play specific episode"""
        self.logger.info(f"Playing episode: {episode.title}")
        # TODO: Implement video player integration

    def play_first_episode(self, series: Series, dialog: Optional[MDDialog] = None):
        """Play first episode of series"""
        if dialog:
            dialog.dismiss()

        if series.seasons:
            first_season = min(series.seasons, key=lambda s: s.season_number)
            if first_season.episodes:
                first_episode = min(first_season.episodes, key=lambda e: e.episode_number)
                self.play_episode(first_episode)
                return

        self.logger.warning(f"No episodes found for series: {series.title}")

    def toggle_favorite(self, series: Series):
        """Toggle series favorite status"""
        series.is_favorite = not series.is_favorite
        self.logger.info(f"Toggled favorite for {series.title}: {series.is_favorite}")
        # TODO: Update in database
        self.update_series_display()

    def show_empty_state(self):
        """Show empty state when no series"""
        empty_layout = MDBoxLayout(
            orientation='vertical',
            spacing=dp(20),
            size_hint=(None, None),
            size=(dp(300), dp(200)),
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )

        empty_icon = MDLabel(
            text="📺",
            halign="center",
            font_size="48sp"
        )

        if self.show_favorites_only:
            empty_text = "No favorite series found"
        elif self.current_category:
            empty_text = f"No series in '{self.current_category}' category"
        else:
            empty_text = "No series available\nAdd a playlist with series content"

        empty_label = MDLabel(
            text=empty_text,
            halign="center",
            theme_text_color="Secondary"
        )

        empty_layout.add_widget(empty_icon)
        empty_layout.add_widget(empty_label)
        self.content_container.add_widget(empty_layout)

    def toggle_view_mode(self, *args):
        """Toggle between view modes"""
        if self.view_mode == ViewMode.GRID:
            self.view_mode = ViewMode.LIST
            self.view_mode_button.icon = "view-list"
        else:
            self.view_mode = ViewMode.GRID
            self.view_mode_button.icon = "view-grid"

        self.update_series_display()

    def show_sort_menu(self, *args):
        """Show sort options menu"""
        # TODO: Implement sort menu similar to VOD browser
        self.logger.info("Sort menu requested")

    def set_sort_order(self, sort_order):
        """Set sort order"""
        self.sort_order = sort_order
        self.filter_and_sort_series()

    def toggle_favorites_filter(self, *args):
        """Toggle favorites filter"""
        self.show_favorites_only = not self.show_favorites_only
        self.favorites_button.icon = "heart" if self.show_favorites_only else "heart-outline"
        self.filter_and_sort_series()

    def show_search(self, *args):
        """Show search interface"""
        # TODO: Implement search
        self.logger.info("Search requested")
