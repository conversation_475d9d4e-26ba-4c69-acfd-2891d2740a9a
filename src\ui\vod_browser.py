"""
VOD browser view for IPTV Player
"""

from typing import List, Optional
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MD<PERSON><PERSON>rollView
from kivymd.uix.gridlayout import MD<PERSON>ridLayout
from kivymd.uix.card import <PERSON><PERSON><PERSON>
from kivymd.uix.label import <PERSON><PERSON>abel
from kivymd.uix.button import MDIconButton, MDFlatButton
from kivymd.uix.chip import <PERSON><PERSON><PERSON>
from kivymd.uix.dialog import MDDialog
from kivymd.uix.progressbar import MD<PERSON>rogressBar
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.menu import MDDropdownMenu
from kivy.metrics import dp

from ..core.logger import LoggerMixin
from ..models.content import ContentInfo, ContentType
from ..models.user_preferences import ViewMode, SortOrder


class VODBrowserView(MDBoxLayout, LoggerMixin):
    """View for browsing VOD content"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.current_playlist = None
        self.vod_content = []
        self.filtered_content = []
        self.current_category = None
        self.view_mode = ViewMode.GRID
        self.sort_order = SortOrder.NAME_ASC
        self.show_favorites_only = False

        self.build_ui()

    def build_ui(self):
        """Build the VOD browser UI"""
        # Filter and sort toolbar
        filter_toolbar = self.create_filter_toolbar()
        self.add_widget(filter_toolbar)

        # Category chips
        self.category_scroll = MDScrollView(
            size_hint_y=None,
            height=dp(60)
        )
        self.category_layout = MDBoxLayout(
            orientation='horizontal',
            spacing=dp(8),
            padding=[dp(16), dp(8)],
            size_hint_x=None,
            adaptive_width=True
        )
        self.category_scroll.add_widget(self.category_layout)
        self.add_widget(self.category_scroll)

        # Content area
        self.content_scroll = MDScrollView()
        self.content_container = MDBoxLayout(orientation='vertical')
        self.content_scroll.add_widget(self.content_container)
        self.add_widget(self.content_scroll)

        # Initially show empty state
        self.show_empty_state()

    def create_filter_toolbar(self):
        """Create filter and sort toolbar"""
        toolbar = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            padding=[dp(16), dp(8)],
            spacing=dp(8)
        )

        # View mode toggle
        self.view_mode_button = MDIconButton(
            icon="view-grid",
            on_release=self.toggle_view_mode
        )
        toolbar.add_widget(self.view_mode_button)

        # Sort button
        self.sort_button = MDFlatButton(
            text="Name ↑",
            on_release=self.show_sort_menu
        )
        toolbar.add_widget(self.sort_button)

        # Spacer
        toolbar.add_widget(MDLabel())

        # Favorites filter
        self.favorites_button = MDIconButton(
            icon="heart-outline",
            on_release=self.toggle_favorites_filter
        )
        toolbar.add_widget(self.favorites_button)

        # Search button
        search_button = MDIconButton(
            icon="magnify",
            on_release=self.show_search
        )
        toolbar.add_widget(search_button)

        return toolbar

    def load_playlist(self, playlist):
        """Load VOD content from playlist"""
        self.current_playlist = playlist
        self.logger.info(f"Loading VOD for playlist: {playlist.name}")

        # TODO: Load VOD content from database
        self.vod_content = []
        self.update_categories()
        self.filter_and_sort_content()

    def update_categories(self):
        """Update category chips"""
        self.category_layout.clear_widgets()

        if not self.vod_content:
            return

        # Get unique categories
        categories = set()
        for content in self.vod_content:
            category = content.genre or "Uncategorized"
            categories.add(category)

        # Add "All" chip
        all_chip = MDChip(
            text="All",
            selected=self.current_category is None,
            on_release=lambda x: self.select_category(None)
        )
        self.category_layout.add_widget(all_chip)

        # Add category chips
        for category in sorted(categories):
            chip = MDChip(
                text=category,
                selected=self.current_category == category,
                on_release=lambda x, cat=category: self.select_category(cat)
            )
            self.category_layout.add_widget(chip)

    def select_category(self, category):
        """Select a category filter"""
        self.current_category = category
        self.update_category_selection()
        self.filter_and_sort_content()

    def update_category_selection(self):
        """Update category chip selection"""
        for chip in self.category_layout.children:
            if isinstance(chip, MDChip):
                if chip.text == "All":
                    chip.selected = self.current_category is None
                else:
                    chip.selected = chip.text == self.current_category

    def filter_and_sort_content(self):
        """Filter and sort content based on current settings"""
        # Start with all content
        filtered = self.vod_content.copy()

        # Apply category filter
        if self.current_category:
            filtered = [c for c in filtered if (c.genre or "Uncategorized") == self.current_category]

        # Apply favorites filter
        if self.show_favorites_only:
            filtered = [c for c in filtered if c.is_favorite]

        # Sort content
        if self.sort_order == SortOrder.NAME_ASC:
            filtered.sort(key=lambda c: c.title.lower())
        elif self.sort_order == SortOrder.NAME_DESC:
            filtered.sort(key=lambda c: c.title.lower(), reverse=True)
        elif self.sort_order == SortOrder.FAVORITES_FIRST:
            filtered.sort(key=lambda c: (not c.is_favorite, c.title.lower()))

        self.filtered_content = filtered
        self.update_content_display()

    def update_content_display(self):
        """Update content display based on view mode"""
        self.content_container.clear_widgets()

        if not self.filtered_content:
            self.show_empty_state()
            return

        if self.view_mode == ViewMode.GRID:
            self.show_grid_view()
        else:
            self.show_list_view()

    def show_grid_view(self):
        """Show content in grid view"""
        grid = MDGridLayout(
            cols=3,
            spacing=dp(12),
            padding=dp(16),
            size_hint_y=None,
            adaptive_height=True
        )

        for content in self.filtered_content:
            card = self.create_content_card(content)
            grid.add_widget(card)

        self.content_container.add_widget(grid)

    def show_list_view(self):
        """Show content in list view"""
        # TODO: Implement list view
        self.show_grid_view()  # Fallback to grid for now

    def create_content_card(self, content: ContentInfo):
        """Create content card for grid view"""
        card = MDCard(
            orientation='vertical',
            size_hint_y=None,
            height=dp(280),
            elevation=3,
            on_release=lambda x: self.show_content_details(content)
        )

        # Poster area
        poster_area = MDCard(
            size_hint_y=None,
            height=dp(200),
            md_bg_color=self.theme_cls.surface_color
        )

        if content.poster:
            # TODO: Load poster image
            poster_placeholder = MDLabel(
                text="🎬",
                halign="center",
                font_size="48sp"
            )
        else:
            poster_placeholder = MDLabel(
                text=content.title[:3].upper(),
                halign="center",
                theme_text_color="Primary",
                font_style="H4"
            )

        poster_area.add_widget(poster_placeholder)
        card.add_widget(poster_area)

        # Content info
        info_layout = MDBoxLayout(
            orientation='vertical',
            padding=dp(8),
            spacing=dp(4)
        )

        # Title
        title_label = MDLabel(
            text=content.title,
            theme_text_color="Primary",
            font_style="Subtitle2",
            size_hint_y=None,
            height=dp(20)
        )
        info_layout.add_widget(title_label)

        # Year and duration
        meta_text = []
        if content.year:
            meta_text.append(str(content.year))
        if content.duration:
            meta_text.append(content.get_formatted_duration())

        if meta_text:
            meta_label = MDLabel(
                text=" • ".join(meta_text),
                theme_text_color="Secondary",
                font_style="Caption",
                size_hint_y=None,
                height=dp(16)
            )
            info_layout.add_widget(meta_label)

        # Progress bar for watched content
        if content.watch_progress > 0:
            progress_bar = MDProgressBar(
                value=content.watch_progress,
                size_hint_y=None,
                height=dp(4)
            )
            info_layout.add_widget(progress_bar)

        # Action buttons
        button_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(32),
            spacing=dp(8)
        )

        # Favorite button
        fav_button = MDIconButton(
            icon="heart" if content.is_favorite else "heart-outline",
            theme_icon_color="Custom",
            icon_color=self.theme_cls.accent_color if content.is_favorite else self.theme_cls.primary_color,
            on_release=lambda x: self.toggle_favorite(content)
        )
        button_layout.add_widget(fav_button)

        # Spacer
        button_layout.add_widget(MDLabel())

        # Play button
        play_button = MDIconButton(
            icon="play",
            theme_icon_color="Custom",
            icon_color=self.theme_cls.primary_color,
            on_release=lambda x: self.play_content(content)
        )
        button_layout.add_widget(play_button)

        info_layout.add_widget(button_layout)
        card.add_widget(info_layout)

        return card

    def show_content_details(self, content: ContentInfo):
        """Show detailed content information"""
        content_layout = MDBoxLayout(
            orientation='vertical',
            spacing=dp(12),
            size_hint_y=None,
            adaptive_height=True
        )

        # Title and metadata
        title_label = MDLabel(
            text=content.title,
            theme_text_color="Primary",
            font_style="H6"
        )
        content_layout.add_widget(title_label)

        # Year, duration, rating
        meta_parts = []
        if content.year:
            meta_parts.append(str(content.year))
        if content.duration:
            meta_parts.append(content.get_formatted_duration())
        if content.rating:
            meta_parts.append(f"Rated {content.rating.value}")

        if meta_parts:
            meta_label = MDLabel(
                text=" • ".join(meta_parts),
                theme_text_color="Secondary",
                font_style="Body2"
            )
            content_layout.add_widget(meta_label)

        # Description
        if content.description:
            desc_label = MDLabel(
                text=content.description,
                theme_text_color="Primary",
                font_style="Body1"
            )
            content_layout.add_widget(desc_label)

        # Genre and country
        if content.genre or content.country:
            info_parts = []
            if content.genre:
                info_parts.append(f"Genre: {content.genre}")
            if content.country:
                info_parts.append(f"Country: {content.country}")

            info_label = MDLabel(
                text=" • ".join(info_parts),
                theme_text_color="Secondary",
                font_style="Caption"
            )
            content_layout.add_widget(info_label)

        # Cast and director
        if content.director or content.cast:
            cast_parts = []
            if content.director:
                cast_parts.append(f"Director: {content.director}")
            if content.cast:
                cast_parts.append(f"Cast: {', '.join(content.cast[:3])}")

            cast_label = MDLabel(
                text="\n".join(cast_parts),
                theme_text_color="Secondary",
                font_style="Caption"
            )
            content_layout.add_widget(cast_label)

        # Watch progress
        if content.watch_progress > 0:
            progress_label = MDLabel(
                text=f"Watched: {content.watch_progress:.1f}%",
                theme_text_color="Secondary",
                font_style="Caption"
            )
            content_layout.add_widget(progress_label)

        # Dialog buttons
        buttons = [
            MDFlatButton(
                text="CLOSE",
                on_release=lambda x: dialog.dismiss()
            ),
            MDFlatButton(
                text="PLAY",
                on_release=lambda x: self.play_content(content, dialog)
            )
        ]

        dialog = MDDialog(
            title="Movie Details",
            type="custom",
            content_cls=content_layout,
            buttons=buttons
        )
        dialog.open()

    def play_content(self, content: ContentInfo, dialog: Optional[MDDialog] = None):
        """Play VOD content"""
        if dialog:
            dialog.dismiss()

        self.logger.info(f"Playing content: {content.title}")
        # TODO: Implement video player integration

    def toggle_favorite(self, content: ContentInfo):
        """Toggle content favorite status"""
        content.is_favorite = not content.is_favorite
        self.logger.info(f"Toggled favorite for {content.title}: {content.is_favorite}")
        # TODO: Update in database
        self.update_content_display()

    def show_empty_state(self):
        """Show empty state when no content"""
        empty_layout = MDBoxLayout(
            orientation='vertical',
            spacing=dp(20),
            size_hint=(None, None),
            size=(dp(300), dp(200)),
            pos_hint={'center_x': 0.5, 'center_y': 0.5}
        )

        empty_icon = MDLabel(
            text="🎬",
            halign="center",
            font_size="48sp"
        )

        if self.show_favorites_only:
            empty_text = "No favorite movies found"
        elif self.current_category:
            empty_text = f"No movies in '{self.current_category}' category"
        else:
            empty_text = "No movies available\nAdd a playlist with VOD content"

        empty_label = MDLabel(
            text=empty_text,
            halign="center",
            theme_text_color="Secondary"
        )

        empty_layout.add_widget(empty_icon)
        empty_layout.add_widget(empty_label)
        self.content_container.add_widget(empty_layout)

    def toggle_view_mode(self, *args):
        """Toggle between view modes"""
        if self.view_mode == ViewMode.GRID:
            self.view_mode = ViewMode.LIST
            self.view_mode_button.icon = "view-list"
        else:
            self.view_mode = ViewMode.GRID
            self.view_mode_button.icon = "view-grid"

        self.update_content_display()

    def show_sort_menu(self, *args):
        """Show sort options menu"""
        menu_items = [
            {"text": "Name ↑", "on_release": lambda: self.set_sort_order(SortOrder.NAME_ASC)},
            {"text": "Name ↓", "on_release": lambda: self.set_sort_order(SortOrder.NAME_DESC)},
            {"text": "Favorites First", "on_release": lambda: self.set_sort_order(SortOrder.FAVORITES_FIRST)},
        ]

        self.sort_menu = MDDropdownMenu(
            caller=self.sort_button,
            items=menu_items,
            width_mult=4
        )
        self.sort_menu.open()

    def set_sort_order(self, sort_order):
        """Set sort order"""
        self.sort_order = sort_order

        # Update button text
        sort_text = {
            SortOrder.NAME_ASC: "Name ↑",
            SortOrder.NAME_DESC: "Name ↓",
            SortOrder.FAVORITES_FIRST: "Favorites First"
        }
        self.sort_button.text = sort_text.get(sort_order, "Name ↑")

        self.sort_menu.dismiss()
        self.filter_and_sort_content()

    def toggle_favorites_filter(self, *args):
        """Toggle favorites filter"""
        self.show_favorites_only = not self.show_favorites_only
        self.favorites_button.icon = "heart" if self.show_favorites_only else "heart-outline"
        self.filter_and_sort_content()

    def show_search(self, *args):
        """Show search interface"""
        # TODO: Implement search
        self.logger.info("Search requested")
