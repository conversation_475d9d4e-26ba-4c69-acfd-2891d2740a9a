"""
Channel List Widget for IPTV Player (PyQt6)
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget,
    QListWidgetItem, QLineEdit, QComboBox, QLabel,
    QPushButton, QGroupBox, QSplitter
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QPixmap

from ..core.logger import LoggerMixin


class ChannelListWidget(QWidget, LoggerMixin):
    """Widget for displaying and managing channel list"""
    
    # Signals
    channel_selected = pyqtSignal(dict)  # Channel data
    
    def __init__(self):
        super().__init__()
        
        self.channels = []
        self.filtered_channels = []
        self.current_playlist = None
        
        self.init_ui()
        self.logger.info("Channel list widget initialized")
    
    def init_ui(self):
        """Initialize the UI"""
        layout = QVBoxLayout(self)
        
        # Search and filter section
        filter_group = QGroupBox("Filter Channels")
        filter_layout = QVBoxLayout(filter_group)
        
        # Search box
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("Search:"))
        
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Search channels...")
        self.search_box.textChanged.connect(self.filter_channels)
        search_layout.addWidget(self.search_box)
        
        filter_layout.addLayout(search_layout)
        
        # Category filter
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel("Category:"))
        
        self.category_combo = QComboBox()
        self.category_combo.addItem("All Categories")
        self.category_combo.currentTextChanged.connect(self.filter_channels)
        category_layout.addWidget(self.category_combo)
        
        filter_layout.addLayout(category_layout)
        
        layout.addWidget(filter_group)
        
        # Channel list
        list_group = QGroupBox("Channels")
        list_layout = QVBoxLayout(list_group)
        
        self.channel_list = QListWidget()
        self.channel_list.itemDoubleClicked.connect(self.on_channel_double_clicked)
        self.channel_list.itemSelectionChanged.connect(self.on_selection_changed)
        list_layout.addWidget(self.channel_list)
        
        # Channel count label
        self.count_label = QLabel("0 channels")
        list_layout.addWidget(self.count_label)
        
        layout.addWidget(list_group)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.play_button = QPushButton("Play Selected")
        self.play_button.setEnabled(False)
        self.play_button.clicked.connect(self.play_selected_channel)
        button_layout.addWidget(self.play_button)
        
        self.favorite_button = QPushButton("Add to Favorites")
        self.favorite_button.setEnabled(False)
        self.favorite_button.clicked.connect(self.toggle_favorite)
        button_layout.addWidget(self.favorite_button)
        
        layout.addLayout(button_layout)
    
    def load_playlist(self, playlist_data):
        """Load channels from playlist"""
        self.logger.info(f"Loading playlist: {playlist_data.get('name', 'Unknown')}")

        self.current_playlist = playlist_data

        try:
            # Parse playlist based on type
            playlist_type = playlist_data.get('type', 'M3U').upper()

            if playlist_type in ['M3U', 'M3U8']:
                self.channels = self.parse_m3u_playlist(playlist_data)
            elif playlist_type == 'XTREAM':
                self.channels = self.parse_xtream_playlist(playlist_data)
            elif playlist_type == 'STALKER':
                self.channels = self.parse_stalker_playlist(playlist_data)
            else:
                self.logger.warning(f"Unknown playlist type: {playlist_type}")
                self.channels = self.simulate_channels()

        except Exception as e:
            self.logger.error(f"Failed to parse playlist: {e}")
            # Show error message to user
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                "Playlist Error",
                f"Failed to load playlist: {str(e)}\n\nUsing demo channels instead."
            )
            # Fallback to simulation for demo purposes
            self.channels = self.simulate_channels()

        # Update category filter
        self.update_categories()

        # Apply current filters
        self.filter_channels()

        self.logger.info(f"Loaded {len(self.channels)} channels")

    def parse_m3u_playlist(self, playlist_data):
        """Parse M3U/M3U8 playlist"""
        url = playlist_data.get('url', '')

        if not url:
            return []

        try:
            # Import here to avoid circular imports
            from ..parsers.m3u_parser import M3UParser
            parser = M3UParser()

            if url.startswith('http'):
                channels, groups = parser.parse_url(url)
            else:
                channels, groups = parser.parse_file(url)

            # Convert Channel objects to dictionaries for UI
            channel_list = []
            for channel in channels:
                channel_dict = {
                    'id': channel.id,
                    'name': channel.name,
                    'url': channel.url,
                    'category': channel.group or 'General',
                    'logo': channel.logo or '',
                    'is_favorite': channel.is_favorite
                }
                channel_list.append(channel_dict)

            return channel_list

        except Exception as e:
            self.logger.error(f"Failed to parse M3U playlist: {e}")
            return []

    def parse_xtream_playlist(self, playlist_data):
        """Parse Xtream Codes playlist"""
        try:
            # This would require async implementation
            # For now, return empty list with error message
            self.logger.warning("Xtream Codes parsing requires async implementation")
            return []
        except Exception as e:
            self.logger.error(f"Failed to parse Xtream playlist: {e}")
            return []

    def parse_stalker_playlist(self, playlist_data):
        """Parse Stalker Portal playlist"""
        try:
            # This would require async implementation
            # For now, return empty list with error message
            self.logger.warning("Stalker Portal parsing requires async implementation")
            return []
        except Exception as e:
            self.logger.error(f"Failed to parse Stalker playlist: {e}")
            return []

    def simulate_channels(self):
        """Simulate channel data (replace with actual playlist parsing)"""
        return [
            {
                'id': '1',
                'name': 'BBC One HD',
                'url': 'http://example.com/bbc1.m3u8',
                'category': 'News',
                'logo': 'http://example.com/bbc1.png',
                'is_favorite': False
            },
            {
                'id': '2',
                'name': 'CNN International',
                'url': 'http://example.com/cnn.m3u8',
                'category': 'News',
                'logo': 'http://example.com/cnn.png',
                'is_favorite': True
            },
            {
                'id': '3',
                'name': 'Discovery Channel',
                'url': 'http://example.com/discovery.m3u8',
                'category': 'Documentary',
                'logo': 'http://example.com/discovery.png',
                'is_favorite': False
            },
            {
                'id': '4',
                'name': 'ESPN HD',
                'url': 'http://example.com/espn.m3u8',
                'category': 'Sports',
                'logo': 'http://example.com/espn.png',
                'is_favorite': False
            },
            {
                'id': '5',
                'name': 'National Geographic',
                'url': 'http://example.com/natgeo.m3u8',
                'category': 'Documentary',
                'logo': 'http://example.com/natgeo.png',
                'is_favorite': True
            }
        ]
    
    def update_categories(self):
        """Update category filter dropdown"""
        categories = set()
        for channel in self.channels:
            if channel.get('category'):
                categories.add(channel['category'])

        # Clear and repopulate combo box
        self.category_combo.clear()
        self.category_combo.addItem("All Categories")

        # Define preferred category order
        preferred_order = [
            'News', 'International News', 'UK News', 'Arabic News',
            'Sports', 'Entertainment', 'Movies', 'Series',
            'Documentary', 'Kids', 'Music', 'Religious',
            'General', 'Other'
        ]

        # Sort categories with preferred order first, then alphabetically
        sorted_categories = []

        # Add categories in preferred order if they exist
        for preferred_cat in preferred_order:
            if preferred_cat in categories:
                sorted_categories.append(preferred_cat)
                categories.remove(preferred_cat)

        # Add remaining categories alphabetically
        sorted_categories.extend(sorted(categories))

        # Add to combo box with channel count
        for category in sorted_categories:
            # Count channels in this category
            count = sum(1 for ch in self.channels if ch.get('category') == category)
            display_text = f"{category} ({count})"
            self.category_combo.addItem(display_text)
    
    def filter_channels(self):
        """Filter channels based on search and category"""
        search_text = self.search_box.text().lower()

        # Get the actual category name from combo box
        current_text = self.category_combo.currentText()
        if current_text == "All Categories":
            selected_category = "All Categories"
        else:
            # Extract category name from "Category Name (count)" format
            selected_category = current_text.split(' (')[0] if ' (' in current_text else current_text

        self.filtered_channels = []

        for channel in self.channels:
            # Check search filter
            if search_text and search_text not in channel['name'].lower():
                continue

            # Check category filter
            if (selected_category != "All Categories" and
                channel.get('category') != selected_category):
                continue
            
            self.filtered_channels.append(channel)

        # Sort filtered channels: favorites first, then alphabetically
        self.filtered_channels.sort(key=lambda ch: (
            not ch.get('is_favorite', False),  # False comes before True, so favorites first
            ch.get('name', '').lower()  # Then alphabetical by name
        ))

        # Update list display
        self.update_channel_list()

        # Update count label
        if selected_category == "All Categories":
            self.count_label.setText(f"{len(self.filtered_channels)} channels")
        else:
            self.count_label.setText(f"{len(self.filtered_channels)} channels in {selected_category}")
    
    def update_channel_list(self):
        """Update the channel list display"""
        self.channel_list.clear()
        
        for channel in self.filtered_channels:
            item = QListWidgetItem()
            
            # Create display text
            display_text = channel['name']
            if channel.get('is_favorite'):
                display_text = f"⭐ {display_text}"
            
            if channel.get('category'):
                display_text += f" ({channel['category']})"
            
            item.setText(display_text)
            item.setData(Qt.ItemDataRole.UserRole, channel)
            
            # Add to list
            self.channel_list.addItem(item)
        
        # Add message if no channels found
        if not self.filtered_channels:
            item = QListWidgetItem()
            item.setText("No channels found in this category")
            item.setFlags(Qt.ItemFlag.NoItemFlags)  # Make it non-selectable
            self.channel_list.addItem(item)
    
    def on_channel_double_clicked(self, item):
        """Handle channel double-click"""
        channel_data = item.data(Qt.ItemDataRole.UserRole)
        if channel_data:
            self.channel_selected.emit(channel_data)
    
    def on_selection_changed(self):
        """Handle selection change"""
        has_selection = bool(self.channel_list.currentItem())
        self.play_button.setEnabled(has_selection)
        self.favorite_button.setEnabled(has_selection)
        
        if has_selection:
            current_item = self.channel_list.currentItem()
            channel_data = current_item.data(Qt.ItemDataRole.UserRole)
            is_favorite = channel_data.get('is_favorite', False)
            self.favorite_button.setText("Remove from Favorites" if is_favorite else "Add to Favorites")
    
    def play_selected_channel(self):
        """Play the currently selected channel"""
        current_item = self.channel_list.currentItem()
        if current_item:
            channel_data = current_item.data(Qt.ItemDataRole.UserRole)
            self.channel_selected.emit(channel_data)
    
    def toggle_favorite(self):
        """Toggle favorite status of selected channel"""
        current_item = self.channel_list.currentItem()
        if current_item:
            channel_data = current_item.data(Qt.ItemDataRole.UserRole)
            channel_data['is_favorite'] = not channel_data.get('is_favorite', False)
            
            # Update display
            self.update_channel_list()
            
            # Restore selection
            for i in range(self.channel_list.count()):
                item = self.channel_list.item(i)
                if item.data(Qt.ItemDataRole.UserRole)['id'] == channel_data['id']:
                    self.channel_list.setCurrentItem(item)
                    break
            
            self.logger.info(f"Toggled favorite for channel: {channel_data['name']}")
    
    def get_selected_channel(self):
        """Get currently selected channel data"""
        current_item = self.channel_list.currentItem()
        if current_item:
            return current_item.data(Qt.ItemDataRole.UserRole)
        return None
    
    def clear_channels(self):
        """Clear all channels"""
        self.channels = []
        self.filtered_channels = []
        self.channel_list.clear()
        self.count_label.setText("0 channels")
        self.category_combo.clear()
        self.category_combo.addItem("All Categories")
